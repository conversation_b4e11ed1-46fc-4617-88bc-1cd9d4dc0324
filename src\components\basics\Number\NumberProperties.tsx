import { NumberPropertiesFormData } from "@/app/(core)/forms/types";
import NumberInput from "@/components/NumberInput";
import { FormElement } from "@/components/types";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useAppDispatch, useAppSelector } from "@/hooks/use-redux";
import { replaceFormElement } from "@/lib/redux/slices/formSlice";
import { SelectedFormElementPayload } from "@/lib/redux/types";
import { findFormElementSectionId } from "@/lib/utils";
import { numberPropertiesSchema } from "@/schemas/properties/numberProperties";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";

const NumberProperties = () => {
  const dispatch = useAppDispatch();
  const selectedFormBuilderItem = useAppSelector(state => state.form.selectedFormBuilderItem) as SelectedFormElementPayload;
  const screenId = useAppSelector(state => state.form.selectedFormBuilderItemScreen);
  const formScreens = useAppSelector(state => state.form.formScreens);
  const elementId = selectedFormBuilderItem?.id ?? "";
  const elementScreen = formScreens.find(screen => screen.id === screenId);
  let sectionId = "";
  if (elementScreen) {
    sectionId = findFormElementSectionId(elementScreen.sections, elementId) ?? "";
  }

  const form = useForm<NumberPropertiesFormData>({
    resolver: zodResolver(numberPropertiesSchema),
    mode: "onChange",
    defaultValues: {
      minimumValue: selectedFormBuilderItem?.minimumValue ?? 0,
      maximumValue: selectedFormBuilderItem?.maximumValue ?? 2,
      currency: selectedFormBuilderItem?.currency || false,
      isDecimal: selectedFormBuilderItem?.isDecimal || false,
      isPercentage: selectedFormBuilderItem?.isPercentage || false,
    },
    shouldFocusError: false,
  });

  const {
    setValue,
    watch,
    formState: { errors, isValid },
  } = form;

  const hasCurrency = watch("currency");
  const hasPercentage = watch("isPercentage");

  useEffect(() => {
    setValue("currency", selectedFormBuilderItem?.currency || false);
    setValue("isDecimal", selectedFormBuilderItem?.isDecimal || false);
    setValue("isPercentage", selectedFormBuilderItem?.isPercentage || false);
    setValue("minimumValue", selectedFormBuilderItem?.minimumValue ?? 0);
    setValue("maximumValue", selectedFormBuilderItem?.maximumValue ?? 2);
  }, [selectedFormBuilderItem, setValue]);

  const updateFormElements = (data: NumberPropertiesFormData) => {
    const newFormElement = {
      ...selectedFormBuilderItem,
      ...data,
    } as FormElement;
    dispatch(replaceFormElement({ screenId, sectionId, element: newFormElement }));
  };

  const handleCurrencyChange = (checked: boolean) => {
    if (checked) {
      setValue("isPercentage", false);
    }
    setValue("currency", checked);
    // Trigger form submission after setting values
    setTimeout(() => {
      form.handleSubmit(updateFormElements)();
    }, 0);
  };

  const handlePercentageChange = (checked: boolean) => {
    if (checked) {
      setValue("currency", false);
    }
    setValue("isPercentage", checked);
    // Trigger form submission after setting values
    setTimeout(() => {
      form.handleSubmit(updateFormElements)();
    }, 0);
  };

  const handleDecimalChange = (checked: boolean) => {
    setValue("isDecimal", checked);
    // Trigger form submission after setting values
    setTimeout(() => {
      form.handleSubmit(updateFormElements)();
    }, 0);
  };

  return (
    <Form {...form}>
      <form onChange={form.handleSubmit(updateFormElements)}>
        <div className="flex flex-col gap-2">
          <div className="flex flex-wrap items-center gap-4">
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-1">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={handleCurrencyChange} disabled={hasPercentage} />
                    </FormControl>
                    <FormLabel>Currency</FormLabel>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isDecimal"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-1">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel>Decimal</FormLabel>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isPercentage"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-1">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={handlePercentageChange} disabled={hasCurrency} />
                    </FormControl>
                    <FormLabel>Percentage</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="minimumValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Minimum Value</FormLabel>
                <FormControl>
                  <NumberInput
                    {...field}
                    value={field.value}
                    placeholder="Minimum Value"
                    onChange={field.onChange}
                    className={`${errors.minimumValue && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="maximumValue"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maximum Value</FormLabel>
                <FormControl>
                  <NumberInput
                    {...field}
                    value={field.value}
                    placeholder="Maximum Value"
                    onChange={field.onChange}
                    className={`${errors.maximumValue && "border-destructive"}`}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
};

export default NumberProperties;
